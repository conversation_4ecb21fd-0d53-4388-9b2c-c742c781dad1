class BilibiliAutoPlayerBackground {
    constructor() {
        this.isRunning = false;
        this.currentVideoIndex = 0;
        this.videoRecords = [];
        this.settings = {};
        this.currentTab = null;
        this.playTimer = null;
        this.videoUrls = [];
        
        this.initMessageListener();
    }
    
    initMessageListener() {
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true; // 保持消息通道开放
        });
    }
    
    async handleMessage(request, sender, sendResponse) {
        try {
            switch (request.action) {
                case 'startAutoPlay':
                    await this.startAutoPlay(request.settings);
                    sendResponse({ success: true });
                    break;
                    
                case 'stopAutoPlay':
                    await this.stopAutoPlay();
                    sendResponse({ success: true });
                    break;
                    
                case 'getStatus':
                    sendResponse({
                        isRunning: this.isRunning,
                        currentVideo: this.getCurrentVideoTitle(),
                        currentIndex: this.currentVideoIndex,
                        records: this.videoRecords
                    });
                    break;
                    
                case 'videoPlayCompleted':
                    await this.handleVideoCompleted(request.data);
                    sendResponse({ success: true });
                    break;
                    
                case 'videoPlayFailed':
                    await this.handleVideoFailed(request.data);
                    sendResponse({ success: true });
                    break;
                    
                default:
                    sendResponse({ error: 'Unknown action' });
            }
        } catch (error) {
            console.error('处理消息失败:', error);
            sendResponse({ error: error.message });
        }
    }
    
    async startAutoPlay(settings) {
        this.settings = settings;
        this.isRunning = true;
        this.currentVideoIndex = 0;
        this.videoRecords = [];
        this.videoUrls = [];
        
        console.log('开始自动播放，设置:', settings);
        
        // 打开B站搜索页面
        await this.openBilibiliSearch();
    }
    
    async stopAutoPlay() {
        this.isRunning = false;
        
        if (this.playTimer) {
            clearTimeout(this.playTimer);
            this.playTimer = null;
        }
        
        console.log('停止自动播放');
    }
    
    async openBilibiliSearch() {
        try {
            // 创建新标签页打开B站搜索
            const searchUrl = `https://search.bilibili.com/all?keyword=${encodeURIComponent(this.settings.searchKeyword)}`;
            
            const tab = await chrome.tabs.create({
                url: searchUrl,
                active: true
            });
            
            this.currentTab = tab;
            
            // 等待页面加载完成后获取视频列表
            setTimeout(async () => {
                await this.getVideoList();
            }, 3000);
            
        } catch (error) {
            console.error('打开B站搜索失败:', error);
            this.isRunning = false;
        }
    }
    
    async getVideoList() {
        try {
            if (!this.currentTab) return;
            
            // 注入脚本获取视频列表
            const results = await chrome.scripting.executeScript({
                target: { tabId: this.currentTab.id },
                function: this.extractVideoUrls
            });
            
            if (results && results[0] && results[0].result) {
                this.videoUrls = results[0].result;
                console.log('获取到视频列表:', this.videoUrls.length, '个视频');
                
                if (this.videoUrls.length > 0) {
                    await this.playNextVideo();
                } else {
                    console.log('未找到视频');
                    this.isRunning = false;
                }
            }
        } catch (error) {
            console.error('获取视频列表失败:', error);
            this.isRunning = false;
        }
    }
    
    // 在页面中执行的函数，提取视频URL
    extractVideoUrls() {
        const videoElements = document.querySelectorAll('.bili-video-card .bili-video-card__wrap > a, .video-item .title > a');
        const urls = [];
        
        videoElements.forEach(element => {
            const href = element.href;
            const title = element.title || element.textContent.trim();
            
            if (href && href.includes('bilibili.com/video/')) {
                urls.push({
                    url: href,
                    title: title
                });
            }
        });
        
        return urls.slice(0, 20); // 最多获取20个视频
    }
    
    async playNextVideo() {
        if (!this.isRunning || this.currentVideoIndex >= Math.min(this.settings.maxVideos, this.videoUrls.length)) {
            console.log('播放完成');
            this.isRunning = false;
            return;
        }
        
        const videoInfo = this.videoUrls[this.currentVideoIndex];
        console.log(`开始播放第 ${this.currentVideoIndex + 1} 个视频:`, videoInfo.title);
        
        try {
            // 关闭当前标签页（如果不是第一个视频）
            if (this.currentTab && this.currentVideoIndex > 0) {
                await chrome.tabs.remove(this.currentTab.id);
            }
            
            // 打开新的视频页面
            const tab = await chrome.tabs.create({
                url: videoInfo.url,
                active: true
            });
            
            this.currentTab = tab;
            
            // 记录开始播放
            const record = {
                title: videoInfo.title,
                url: videoInfo.url,
                startTime: Date.now(),
                playedDuration: 0,
                status: 'playing'
            };
            
            this.videoRecords.push(record);
            
            // 等待页面加载后开始播放
            setTimeout(async () => {
                await this.startVideoPlayback();
            }, 3000);
            
        } catch (error) {
            console.error('播放视频失败:', error);
            await this.handleVideoFailed({
                title: videoInfo.title,
                error: error.message
            });
        }
    }
    
    async startVideoPlayback() {
        try {
            if (!this.currentTab) return;
            
            // 注入脚本开始播放视频
            await chrome.scripting.executeScript({
                target: { tabId: this.currentTab.id },
                function: this.playVideo
            });
            
            // 设置播放时长定时器
            this.playTimer = setTimeout(async () => {
                await this.completeCurrentVideo();
            }, this.settings.playDuration * 1000);
            
        } catch (error) {
            console.error('开始视频播放失败:', error);
            await this.handleVideoFailed({
                title: this.getCurrentVideoTitle(),
                error: error.message
            });
        }
    }
    
    // 在视频页面中执行的播放函数
    playVideo() {
        try {
            // 查找视频元素
            const video = document.querySelector('video');
            if (video) {
                // 设置音量为较低值
                video.volume = 0.1;
                
                // 开始播放
                video.play().then(() => {
                    console.log('视频开始播放');
                }).catch(error => {
                    console.error('播放失败:', error);
                });
                
                // 尝试点击播放按钮（如果视频没有自动播放）
                setTimeout(() => {
                    const playBtn = document.querySelector('.bpx-player-ctrl-play');
                    if (playBtn && video.paused) {
                        playBtn.click();
                    }
                }, 1000);
            } else {
                console.error('未找到视频元素');
            }
        } catch (error) {
            console.error('播放视频时出错:', error);
        }
    }
    
    async completeCurrentVideo() {
        const currentRecord = this.videoRecords[this.currentVideoIndex];
        if (currentRecord) {
            currentRecord.status = 'success';
            currentRecord.playedDuration = this.settings.playDuration;
        }
        
        console.log(`视频播放完成: ${this.getCurrentVideoTitle()}`);
        
        this.currentVideoIndex++;
        
        // 等待一段时间后播放下一个视频
        setTimeout(async () => {
            await this.playNextVideo();
        }, this.settings.delayBetween * 1000);
    }
    
    async handleVideoCompleted(data) {
        const currentRecord = this.videoRecords[this.currentVideoIndex];
        if (currentRecord) {
            currentRecord.status = 'success';
            currentRecord.playedDuration = data.duration || this.settings.playDuration;
        }
        
        this.currentVideoIndex++;
        await this.playNextVideo();
    }
    
    async handleVideoFailed(data) {
        const currentRecord = this.videoRecords[this.currentVideoIndex];
        if (currentRecord) {
            currentRecord.status = 'failed';
            currentRecord.error = data.error;
        }
        
        console.log(`视频播放失败: ${data.title}, 错误: ${data.error}`);
        
        this.currentVideoIndex++;
        
        // 等待一段时间后播放下一个视频
        setTimeout(async () => {
            await this.playNextVideo();
        }, this.settings.delayBetween * 1000);
    }
    
    getCurrentVideoTitle() {
        if (this.currentVideoIndex < this.videoUrls.length) {
            return this.videoUrls[this.currentVideoIndex]?.title || '未知视频';
        }
        return '无';
    }
}

// 初始化background script
new BilibiliAutoPlayerBackground();

// 监听标签页更新
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete' && tab.url && tab.url.includes('bilibili.com')) {
        // 页面加载完成，可以进行后续操作
        console.log('B站页面加载完成:', tab.url);
    }
});
