// 全局变量
let scene, camera, renderer, controls;
let textMesh, particleSystem;
let animationEnabled = true;
let colorIndex = 0;
let particlesEnabled = true;

// 颜色主题数组
const colorThemes = [
    { primary: 0xff6b6b, secondary: 0x4ecdc4, accent: 0xffe66d },
    { primary: 0x667eea, secondary: 0x764ba2, accent: 0xf093fb },
    { primary: 0x4facfe, secondary: 0x00f2fe, accent: 0x43e97b },
    { primary: 0xfa709a, secondary: 0xfee140, accent: 0x96fbc4 }
];

// 初始化场景
function init() {
    // 创建场景
    scene = new THREE.Scene();
    scene.fog = new THREE.Fog(0x000000, 50, 200);

    // 创建相机
    camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.set(0, 0, 50);

    // 创建渲染器
    renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setClearColor(0x000000, 0);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    document.getElementById('container').appendChild(renderer.domElement);

    // 创建控制器
    controls = new THREE.OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;
    controls.maxDistance = 100;
    controls.minDistance = 20;

    // 添加灯光
    setupLights();
    
    // 创建粒子系统
    createParticleSystem();
    
    // 加载字体并创建文字
    loadFontAndCreateText();
    
    // 开始渲染循环
    animate();
    
    // 添加窗口大小调整监听
    window.addEventListener('resize', onWindowResize, false);
}

// 设置灯光
function setupLights() {
    // 环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    scene.add(ambientLight);

    // 主光源
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(50, 50, 50);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    scene.add(directionalLight);

    // 点光源1 - 彩色
    const pointLight1 = new THREE.PointLight(0xff6b6b, 1, 100);
    pointLight1.position.set(-30, 20, 30);
    scene.add(pointLight1);

    // 点光源2 - 彩色
    const pointLight2 = new THREE.PointLight(0x4ecdc4, 1, 100);
    pointLight2.position.set(30, -20, 30);
    scene.add(pointLight2);

    // 聚光灯
    const spotLight = new THREE.SpotLight(0xffffff, 1);
    spotLight.position.set(0, 50, 50);
    spotLight.angle = Math.PI / 6;
    spotLight.penumbra = 0.1;
    spotLight.decay = 2;
    spotLight.distance = 200;
    spotLight.castShadow = true;
    scene.add(spotLight);
}

// 创建粒子系统
function createParticleSystem() {
    const particleCount = 1000;
    const particles = new THREE.BufferGeometry();
    const positions = new Float32Array(particleCount * 3);
    const colors = new Float32Array(particleCount * 3);
    const sizes = new Float32Array(particleCount);

    for (let i = 0; i < particleCount; i++) {
        const i3 = i * 3;
        
        // 随机位置
        positions[i3] = (Math.random() - 0.5) * 200;
        positions[i3 + 1] = (Math.random() - 0.5) * 200;
        positions[i3 + 2] = (Math.random() - 0.5) * 200;
        
        // 随机颜色
        const color = new THREE.Color();
        color.setHSL(Math.random(), 0.8, 0.8);
        colors[i3] = color.r;
        colors[i3 + 1] = color.g;
        colors[i3 + 2] = color.b;
        
        // 随机大小
        sizes[i] = Math.random() * 3 + 1;
    }

    particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    particles.setAttribute('color', new THREE.BufferAttribute(colors, 3));
    particles.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

    const particleMaterial = new THREE.PointsMaterial({
        size: 2,
        vertexColors: true,
        transparent: true,
        opacity: 0.8,
        sizeAttenuation: true
    });

    particleSystem = new THREE.Points(particles, particleMaterial);
    scene.add(particleSystem);
}

// 加载字体并创建3D文字
function loadFontAndCreateText() {
    const loader = new THREE.FontLoader();
    
    // 使用在线字体文件
    loader.load('https://threejs.org/examples/fonts/helvetiker_regular.typeface.json', function(font) {
        createText(font);
        document.getElementById('loading').style.display = 'none';
    }, undefined, function(error) {
        console.error('字体加载失败:', error);
        // 如果字体加载失败，创建简单的几何体作为替代
        createFallbackText();
        document.getElementById('loading').style.display = 'none';
    });
}

// 创建3D文字
function createText(font) {
    const textGeometry = new THREE.TextGeometry('邱大山你真棒！', {
        font: font,
        size: 8,
        height: 2,
        curveSegments: 12,
        bevelEnabled: true,
        bevelThickness: 0.5,
        bevelSize: 0.3,
        bevelOffset: 0,
        bevelSegments: 8
    });

    // 居中文字
    textGeometry.computeBoundingBox();
    const centerOffsetX = -0.5 * (textGeometry.boundingBox.max.x - textGeometry.boundingBox.min.x);
    const centerOffsetY = -0.5 * (textGeometry.boundingBox.max.y - textGeometry.boundingBox.min.y);
    textGeometry.translate(centerOffsetX, centerOffsetY, 0);

    // 创建渐变材质
    const textMaterial = createGradientMaterial();
    
    textMesh = new THREE.Mesh(textGeometry, textMaterial);
    textMesh.castShadow = true;
    textMesh.receiveShadow = true;
    scene.add(textMesh);
}

// 创建备用文字（如果字体加载失败）
function createFallbackText() {
    const geometry = new THREE.BoxGeometry(30, 8, 4);
    const material = createGradientMaterial();
    textMesh = new THREE.Mesh(geometry, material);
    textMesh.castShadow = true;
    textMesh.receiveShadow = true;
    scene.add(textMesh);
}

// 创建渐变材质
function createGradientMaterial() {
    const theme = colorThemes[colorIndex];

    // 创建渐变纹理
    const canvas = document.createElement('canvas');
    canvas.width = 256;
    canvas.height = 256;
    const context = canvas.getContext('2d');

    const gradient = context.createLinearGradient(0, 0, 256, 256);
    gradient.addColorStop(0, `#${theme.primary.toString(16).padStart(6, '0')}`);
    gradient.addColorStop(0.5, `#${theme.secondary.toString(16).padStart(6, '0')}`);
    gradient.addColorStop(1, `#${theme.accent.toString(16).padStart(6, '0')}`);

    context.fillStyle = gradient;
    context.fillRect(0, 0, 256, 256);

    const texture = new THREE.CanvasTexture(canvas);

    return new THREE.MeshPhongMaterial({
        map: texture,
        shininess: 100,
        specular: 0x222222,
        transparent: true,
        opacity: 0.9
    });
}

// 动画循环
function animate() {
    requestAnimationFrame(animate);

    if (animationEnabled && textMesh) {
        // 文字旋转动画
        textMesh.rotation.x += 0.005;
        textMesh.rotation.y += 0.01;

        // 文字浮动动画
        textMesh.position.y = Math.sin(Date.now() * 0.002) * 2;
        textMesh.position.z = Math.cos(Date.now() * 0.001) * 1;

        // 文字缩放动画
        const scale = 1 + Math.sin(Date.now() * 0.003) * 0.1;
        textMesh.scale.set(scale, scale, scale);
    }

    // 粒子动画
    if (particlesEnabled && particleSystem) {
        particleSystem.rotation.x += 0.001;
        particleSystem.rotation.y += 0.002;

        const positions = particleSystem.geometry.attributes.position.array;
        for (let i = 0; i < positions.length; i += 3) {
            positions[i + 1] += Math.sin(Date.now() * 0.001 + i) * 0.01;
        }
        particleSystem.geometry.attributes.position.needsUpdate = true;
    }

    // 更新控制器
    controls.update();

    // 渲染场景
    renderer.render(scene, camera);
}

// 窗口大小调整
function onWindowResize() {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
}

// 控制函数
function toggleAnimation() {
    animationEnabled = !animationEnabled;
}

function changeColor() {
    colorIndex = (colorIndex + 1) % colorThemes.length;
    if (textMesh) {
        textMesh.material = createGradientMaterial();
    }
}

function toggleParticles() {
    particlesEnabled = !particlesEnabled;
    if (particleSystem) {
        particleSystem.visible = particlesEnabled;
    }
}

// 启动应用
init();
