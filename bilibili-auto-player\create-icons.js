const fs = require('fs');
const path = require('path');

// 创建一个简单的PNG图标生成器
function createIcon(size) {
    // 创建一个简单的SVG图标
    const svg = `
    <svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
        <defs>
            <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#00a1d6;stop-opacity:1" />
                <stop offset="100%" style="stop-color:#0084c7;stop-opacity:1" />
            </linearGradient>
        </defs>
        
        <!-- 背景 -->
        <rect width="${size}" height="${size}" fill="url(#bg)" rx="${size * 0.1}"/>
        
        <!-- 播放按钮 -->
        <polygon points="${size * 0.35},${size * 0.25} ${size * 0.35},${size * 0.75} ${size * 0.7},${size * 0.5}" 
                 fill="white" opacity="0.9"/>
        
        <!-- 装饰圆环 -->
        <circle cx="${size / 2}" cy="${size / 2}" r="${size * 0.35}" 
                fill="none" stroke="white" stroke-width="${Math.max(1, size * 0.02)}" opacity="0.6"/>
        
        <!-- 装饰点 -->
        <circle cx="${size * 0.75}" cy="${size * 0.25}" r="${size * 0.03}" fill="white" opacity="0.8"/>
        <circle cx="${size * 0.25}" cy="${size * 0.75}" r="${size * 0.03}" fill="white" opacity="0.8"/>
        
        <!-- B站风格文字 -->
        <text x="${size / 2}" y="${size * 0.9}" text-anchor="middle" 
              font-family="Arial, sans-serif" font-size="${size * 0.1}" fill="white" opacity="0.7">AUTO</text>
    </svg>`;
    
    return svg;
}

// 创建icons目录
const iconsDir = path.join(__dirname, 'icons');
if (!fs.existsSync(iconsDir)) {
    fs.mkdirSync(iconsDir);
}

// 生成不同尺寸的SVG图标
const sizes = [16, 32, 48, 128];

sizes.forEach(size => {
    const svg = createIcon(size);
    const filename = path.join(iconsDir, `icon${size}.svg`);
    fs.writeFileSync(filename, svg);
    console.log(`生成图标: ${filename}`);
});

console.log('所有图标生成完成！');
console.log('注意：Chrome扩展需要PNG格式图标，请使用在线工具将SVG转换为PNG，或使用generate-icons.html页面生成。');
