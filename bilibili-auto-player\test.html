<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>B站自动播放器测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #fff;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border-left: 4px solid #4ecdc4;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #4ecdc4;
        }
        
        .btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #2196F3, #1976D2);
        }
        
        .btn-secondary:hover {
            box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
        }
        
        .status {
            background: rgba(0, 0, 0, 0.2);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        
        .success {
            color: #4CAF50;
        }
        
        .error {
            color: #f44336;
        }
        
        .info {
            color: #2196F3;
        }
        
        .checklist {
            list-style: none;
            padding: 0;
        }
        
        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .checklist li:before {
            content: "✓ ";
            color: #4CAF50;
            font-weight: bold;
            margin-right: 8px;
        }
        
        .warning {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid #ffc107;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 B站自动播放器测试页面</h1>
        
        <div class="test-section">
            <h3>📋 安装检查清单</h3>
            <ul class="checklist">
                <li>Chrome扩展已安装并启用</li>
                <li>图标文件已生成（icon16.png, icon32.png, icon48.png, icon128.png）</li>
                <li>扩展权限已授予</li>
                <li>开发者模式已开启</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>🔧 功能测试</h3>
            
            <h4>1. 扩展基础功能</h4>
            <button class="btn" onclick="testExtensionInstalled()">检查扩展是否安装</button>
            <button class="btn btn-secondary" onclick="testPopupOpen()">测试Popup页面</button>
            <div id="extensionStatus" class="status"></div>
            
            <h4>2. 存储功能测试</h4>
            <button class="btn" onclick="testStorage()">测试数据存储</button>
            <button class="btn btn-secondary" onclick="clearStorage()">清除存储数据</button>
            <div id="storageStatus" class="status"></div>
            
            <h4>3. 消息通信测试</h4>
            <button class="btn" onclick="testMessaging()">测试消息通信</button>
            <div id="messagingStatus" class="status"></div>
        </div>
        
        <div class="test-section">
            <h3>🌐 B站页面测试</h3>
            <p>点击下面的按钮打开B站页面进行实际测试：</p>
            
            <button class="btn" onclick="openBilibiliSearch()">打开B站搜索页面</button>
            <button class="btn btn-secondary" onclick="openBilibiliVideo()">打开B站视频页面</button>
            
            <div class="warning">
                <strong>注意：</strong>
                <ul>
                    <li>确保已安装扩展后再进行B站页面测试</li>
                    <li>在B站页面按F12查看控制台日志</li>
                    <li>检查扩展图标是否出现在工具栏中</li>
                </ul>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📊 测试结果</h3>
            <div id="testResults" class="status">
                等待测试...
            </div>
        </div>
        
        <div class="test-section">
            <h3>🆘 故障排除</h3>
            <h4>常见问题：</h4>
            <ul>
                <li><strong>扩展无法加载：</strong>检查图标文件是否存在，manifest.json语法是否正确</li>
                <li><strong>Popup无法打开：</strong>检查popup.html路径，确保文件存在</li>
                <li><strong>权限被拒绝：</strong>在扩展管理页面重新授予权限</li>
                <li><strong>脚本注入失败：</strong>检查content.js和injected.js文件</li>
            </ul>
            
            <h4>调试步骤：</h4>
            <ol>
                <li>打开 chrome://extensions/ 查看扩展状态</li>
                <li>点击"检查视图"查看后台脚本日志</li>
                <li>在B站页面按F12查看内容脚本日志</li>
                <li>检查Network标签页的网络请求</li>
            </ol>
        </div>
    </div>
    
    <script>
        let testResults = [];
        
        function updateResults() {
            const resultsDiv = document.getElementById('testResults');
            if (testResults.length === 0) {
                resultsDiv.innerHTML = '等待测试...';
                return;
            }
            
            const html = testResults.map(result => 
                `<div class="${result.status}">${result.message}</div>`
            ).join('');
            resultsDiv.innerHTML = html;
        }
        
        function addResult(message, status = 'info') {
            testResults.push({ message, status, time: new Date().toLocaleTimeString() });
            updateResults();
        }
        
        function testExtensionInstalled() {
            const statusDiv = document.getElementById('extensionStatus');
            
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                try {
                    chrome.runtime.sendMessage('test', (response) => {
                        if (chrome.runtime.lastError) {
                            statusDiv.innerHTML = '<div class="error">扩展未安装或未启用</div>';
                            addResult('❌ 扩展检查失败：' + chrome.runtime.lastError.message, 'error');
                        } else {
                            statusDiv.innerHTML = '<div class="success">扩展已安装并正常运行</div>';
                            addResult('✅ 扩展检查成功', 'success');
                        }
                    });
                } catch (error) {
                    statusDiv.innerHTML = '<div class="error">扩展检查失败：' + error.message + '</div>';
                    addResult('❌ 扩展检查异常：' + error.message, 'error');
                }
            } else {
                statusDiv.innerHTML = '<div class="error">Chrome扩展API不可用</div>';
                addResult('❌ Chrome扩展API不可用', 'error');
            }
        }
        
        function testPopupOpen() {
            addResult('💡 请手动点击扩展图标测试Popup页面', 'info');
        }
        
        function testStorage() {
            const statusDiv = document.getElementById('storageStatus');
            
            if (typeof chrome !== 'undefined' && chrome.storage) {
                const testData = { test: 'data', timestamp: Date.now() };
                
                chrome.storage.local.set(testData, () => {
                    if (chrome.runtime.lastError) {
                        statusDiv.innerHTML = '<div class="error">存储测试失败：' + chrome.runtime.lastError.message + '</div>';
                        addResult('❌ 存储测试失败', 'error');
                    } else {
                        chrome.storage.local.get(['test'], (result) => {
                            if (result.test === 'data') {
                                statusDiv.innerHTML = '<div class="success">存储功能正常</div>';
                                addResult('✅ 存储功能测试成功', 'success');
                            } else {
                                statusDiv.innerHTML = '<div class="error">存储数据读取失败</div>';
                                addResult('❌ 存储数据读取失败', 'error');
                            }
                        });
                    }
                });
            } else {
                statusDiv.innerHTML = '<div class="error">Chrome存储API不可用</div>';
                addResult('❌ Chrome存储API不可用', 'error');
            }
        }
        
        function clearStorage() {
            if (typeof chrome !== 'undefined' && chrome.storage) {
                chrome.storage.local.clear(() => {
                    document.getElementById('storageStatus').innerHTML = '<div class="info">存储数据已清除</div>';
                    addResult('🗑️ 存储数据已清除', 'info');
                });
            }
        }
        
        function testMessaging() {
            const statusDiv = document.getElementById('messagingStatus');
            
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                chrome.runtime.sendMessage({ action: 'test', data: 'hello' }, (response) => {
                    if (chrome.runtime.lastError) {
                        statusDiv.innerHTML = '<div class="error">消息通信失败：' + chrome.runtime.lastError.message + '</div>';
                        addResult('❌ 消息通信测试失败', 'error');
                    } else {
                        statusDiv.innerHTML = '<div class="success">消息通信正常</div>';
                        addResult('✅ 消息通信测试成功', 'success');
                    }
                });
            } else {
                statusDiv.innerHTML = '<div class="error">Chrome运行时API不可用</div>';
                addResult('❌ Chrome运行时API不可用', 'error');
            }
        }
        
        function openBilibiliSearch() {
            const url = 'https://search.bilibili.com/all?keyword=mcp技术';
            window.open(url, '_blank');
            addResult('🔍 已打开B站搜索页面，请检查扩展功能', 'info');
        }
        
        function openBilibiliVideo() {
            const url = 'https://www.bilibili.com/video/BV1GJ411x7h7';
            window.open(url, '_blank');
            addResult('🎬 已打开B站视频页面，请检查扩展功能', 'info');
        }
        
        // 页面加载完成后自动运行基础检查
        document.addEventListener('DOMContentLoaded', () => {
            addResult('🚀 测试页面加载完成', 'success');
            setTimeout(() => {
                testExtensionInstalled();
            }, 1000);
        });
    </script>
</body>
</html>
