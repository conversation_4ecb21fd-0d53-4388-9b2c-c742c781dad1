class BilibiliContentScript {
    constructor() {
        this.isVideoPage = false;
        this.isSearchPage = false;
        this.videoElement = null;
        this.playStartTime = null;
        
        this.init();
    }
    
    init() {
        // 检测页面类型
        this.detectPageType();

        // 注入增强脚本
        this.injectEnhancedScript();

        // 如果是视频页面，监听视频元素
        if (this.isVideoPage) {
            this.initVideoPage();
        }

        // 如果是搜索页面，准备视频列表提取
        if (this.isSearchPage) {
            this.initSearchPage();
        }

        // 监听来自background的消息
        this.initMessageListener();

        // 监听来自注入脚本的消息
        this.initInjectedScriptListener();

        console.log('B站内容脚本初始化完成', {
            isVideoPage: this.isVideoPage,
            isSearchPage: this.isSearchPage,
            url: window.location.href
        });
    }
    
    detectPageType() {
        const url = window.location.href;
        this.isVideoPage = url.includes('/video/');
        this.isSearchPage = url.includes('search.bilibili.com');
    }

    injectEnhancedScript() {
        // 注入增强脚本到页面
        const script = document.createElement('script');
        script.src = chrome.runtime.getURL('injected.js');
        script.onload = function() {
            this.remove();
        };
        (document.head || document.documentElement).appendChild(script);
        console.log('注入增强脚本');
    }

    initInjectedScriptListener() {
        // 监听来自注入脚本的消息
        window.addEventListener('message', (event) => {
            if (event.source !== window) return;

            const { type, event: eventType, data } = event.data;

            if (type === 'BILIBILI_EXTENSION_EVENT') {
                this.handleInjectedEvent(eventType, data);
            } else if (type === 'BILIBILI_VIDEO_INFO') {
                this.handleVideoInfo(data);
            }
        });
    }

    handleInjectedEvent(eventType, data) {
        console.log('收到注入脚本事件:', eventType, data);

        switch (eventType) {
            case 'videoLoaded':
                this.notifyBackground('videoLoaded', data);
                break;
            case 'videoPlay':
                this.notifyBackground('videoPlay', data);
                break;
            case 'videoEnded':
                this.notifyBackground('videoEnded', data);
                break;
            case 'videoError':
                this.notifyBackground('videoError', data);
                break;
            case 'searchResultsReady':
                this.notifyBackground('searchResultsReady', data);
                break;
        }
    }

    handleVideoInfo(data) {
        this.lastVideoInfo = data;
    }

    notifyBackground(event, data) {
        chrome.runtime.sendMessage({
            action: 'injectedEvent',
            event: event,
            data: data
        }).catch(error => {
            console.error('发送消息到background失败:', error);
        });
    }
    
    initVideoPage() {
        // 等待视频元素加载
        this.waitForVideoElement();
        
        // 监听页面变化
        this.observePageChanges();
    }
    
    initSearchPage() {
        // 搜索页面初始化
        console.log('搜索页面初始化');
        
        // 等待搜索结果加载
        this.waitForSearchResults();
    }
    
    waitForVideoElement() {
        const checkVideo = () => {
            this.videoElement = document.querySelector('video');
            
            if (this.videoElement) {
                console.log('找到视频元素');
                this.setupVideoListeners();
            } else {
                // 继续等待
                setTimeout(checkVideo, 1000);
            }
        };
        
        checkVideo();
    }
    
    setupVideoListeners() {
        if (!this.videoElement) return;
        
        // 监听视频事件
        this.videoElement.addEventListener('loadeddata', () => {
            console.log('视频数据加载完成');
        });
        
        this.videoElement.addEventListener('play', () => {
            console.log('视频开始播放');
            this.playStartTime = Date.now();
        });
        
        this.videoElement.addEventListener('pause', () => {
            console.log('视频暂停');
        });
        
        this.videoElement.addEventListener('ended', () => {
            console.log('视频播放结束');
            this.notifyVideoCompleted();
        });
        
        this.videoElement.addEventListener('error', (error) => {
            console.error('视频播放错误:', error);
            this.notifyVideoFailed(error.message);
        });
    }
    
    waitForSearchResults() {
        const checkResults = () => {
            const videoCards = document.querySelectorAll('.bili-video-card, .video-item');
            
            if (videoCards.length > 0) {
                console.log(`找到 ${videoCards.length} 个视频卡片`);
            } else {
                // 继续等待
                setTimeout(checkResults, 1000);
            }
        };
        
        // 延迟检查，等待页面完全加载
        setTimeout(checkResults, 2000);
    }
    
    observePageChanges() {
        // 监听DOM变化，以防页面动态加载内容
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    // 检查是否有新的视频元素
                    if (!this.videoElement) {
                        this.waitForVideoElement();
                    }
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    initMessageListener() {
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true;
        });
    }
    
    handleMessage(request, sender, sendResponse) {
        switch (request.action) {
            case 'playVideo':
                this.playVideo();
                sendResponse({ success: true });
                break;
                
            case 'pauseVideo':
                this.pauseVideo();
                sendResponse({ success: true });
                break;
                
            case 'getVideoInfo':
                sendResponse(this.getVideoInfo());
                break;
                
            case 'extractVideoUrls':
                sendResponse(this.extractVideoUrls());
                break;
                
            default:
                sendResponse({ error: 'Unknown action' });
        }
    }
    
    playVideo() {
        try {
            // 首先尝试通过注入脚本播放
            window.postMessage({
                type: 'BILIBILI_AUTO_PLAY'
            }, '*');

            // 备用方案：直接操作视频元素
            setTimeout(() => {
                if (this.videoElement) {
                    this.videoElement.volume = 0.1;
                    this.videoElement.play().then(() => {
                        console.log('视频播放成功');
                        this.playStartTime = Date.now();
                    }).catch(error => {
                        console.error('视频播放失败:', error);
                        this.clickPlayButton();
                    });
                } else {
                    this.clickPlayButton();
                }
            }, 1000);
        } catch (error) {
            console.error('播放视频时出错:', error);
            this.notifyVideoFailed(error.message);
        }
    }
    
    pauseVideo() {
        if (this.videoElement) {
            this.videoElement.pause();
        }
    }
    
    clickPlayButton() {
        // 尝试多种播放按钮选择器
        const playButtonSelectors = [
            '.bpx-player-ctrl-play',
            '.bilibili-player-video-btn-start',
            '.bpx-player-ctrl-btn',
            '[aria-label="播放"]',
            '.play-btn'
        ];
        
        for (const selector of playButtonSelectors) {
            const button = document.querySelector(selector);
            if (button) {
                console.log('点击播放按钮:', selector);
                button.click();
                break;
            }
        }
        
        // 如果还是没有找到，尝试等待后再次查找视频元素
        setTimeout(() => {
            this.waitForVideoElement();
        }, 2000);
    }
    
    getVideoInfo() {
        const title = document.querySelector('h1[title], .video-title, .media-title')?.textContent?.trim() || '未知标题';
        const duration = this.videoElement?.duration || 0;
        const currentTime = this.videoElement?.currentTime || 0;
        
        return {
            title,
            duration,
            currentTime,
            isPlaying: this.videoElement ? !this.videoElement.paused : false
        };
    }
    
    extractVideoUrls() {
        const videoElements = document.querySelectorAll('.bili-video-card .bili-video-card__wrap > a, .video-item .title > a, .bili-video-card__info--right > a');
        const urls = [];
        
        videoElements.forEach(element => {
            const href = element.href;
            let title = element.title || element.getAttribute('title');
            
            // 如果没有title属性，尝试从文本内容获取
            if (!title) {
                const titleElement = element.querySelector('.bili-video-card__info--tit, .title');
                title = titleElement ? titleElement.textContent.trim() : element.textContent.trim();
            }
            
            if (href && href.includes('bilibili.com/video/') && title) {
                urls.push({
                    url: href,
                    title: title.substring(0, 100) // 限制标题长度
                });
            }
        });
        
        console.log(`提取到 ${urls.length} 个视频链接`);
        return urls.slice(0, 20); // 最多返回20个
    }
    
    notifyVideoCompleted() {
        const playedDuration = this.playStartTime ? Math.floor((Date.now() - this.playStartTime) / 1000) : 0;
        
        chrome.runtime.sendMessage({
            action: 'videoPlayCompleted',
            data: {
                title: this.getVideoInfo().title,
                duration: playedDuration
            }
        }).catch(error => {
            console.error('发送视频完成消息失败:', error);
        });
    }
    
    notifyVideoFailed(error) {
        chrome.runtime.sendMessage({
            action: 'videoPlayFailed',
            data: {
                title: this.getVideoInfo().title,
                error: error
            }
        }).catch(err => {
            console.error('发送视频失败消息失败:', err);
        });
    }
}

// 等待DOM加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new BilibiliContentScript();
    });
} else {
    new BilibiliContentScript();
}
