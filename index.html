<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D酷炫文字效果 - 邱大山你真棒！</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: linear-gradient(45deg, #000428, #004e92);
            font-family: 'Arial', sans-serif;
        }
        
        #container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }
        
        #info {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            z-index: 100;
            font-size: 14px;
            background: rgba(0,0,0,0.5);
            padding: 10px;
            border-radius: 5px;
        }
        
        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 24px;
            z-index: 200;
        }
        
        .controls {
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: white;
            z-index: 100;
            background: rgba(0,0,0,0.5);
            padding: 15px;
            border-radius: 5px;
        }
        
        .control-button {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border: none;
            color: white;
            padding: 8px 16px;
            margin: 5px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 12px;
            transition: transform 0.2s;
        }
        
        .control-button:hover {
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="loading">加载中...</div>
        <div id="info">
            <h3>🎉 3D酷炫文字效果</h3>
            <p><strong>文字内容: 邱大山你真棒！</strong></p>
            <p>• 鼠标拖拽旋转视角</p>
            <p>• 滚轮缩放</p>
            <p>• 自动动画效果</p>
            <p><a href="test.html" style="color: #4ecdc4;">测试中文显示</a></p>
        </div>
        <div class="controls">
            <button class="control-button" onclick="toggleAnimation()">切换动画</button>
            <button class="control-button" onclick="changeColor()">变换颜色</button>
            <button class="control-button" onclick="toggleParticles()">粒子效果</button>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/loaders/FontLoader.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/geometries/TextGeometry.js"></script>
    <script src="script.js?v=2"></script>
</body>
</html>
