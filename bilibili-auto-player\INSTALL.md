# B站视频自动播放器 - 安装指南

## 🚀 快速安装

### 第一步：准备图标文件

由于图标文件需要PNG格式，请选择以下方法之一生成图标：

#### 方法1：使用Python生成（推荐）
```bash
# 安装PIL库
pip install Pillow

# 运行图标生成脚本
python create_icons.py
```

#### 方法2：使用浏览器生成
1. 打开 `icons/create_simple_icons.html` 文件
2. 点击"生成并下载所有图标"按钮
3. 将下载的PNG文件放入 `icons/` 文件夹

#### 方法3：手动创建
创建以下尺寸的PNG图标文件并放入 `icons/` 文件夹：
- `icon16.png` (16x16像素)
- `icon32.png` (32x32像素)  
- `icon48.png` (48x48像素)
- `icon128.png` (128x128像素)

### 第二步：安装Chrome扩展

1. **打开Chrome扩展管理页面**
   - 在地址栏输入：`chrome://extensions/`
   - 或者：菜单 → 更多工具 → 扩展程序

2. **开启开发者模式**
   - 点击右上角的"开发者模式"开关

3. **加载扩展**
   - 点击"加载已解压的扩展程序"
   - 选择 `bilibili-auto-player` 文件夹
   - 点击"选择文件夹"

4. **确认安装**
   - 扩展应该出现在扩展列表中
   - 确保扩展已启用（开关为蓝色）

## 🎯 使用方法

### 基本操作

1. **打开扩展**
   - 点击Chrome工具栏中的扩展图标
   - 或者点击拼图图标，然后选择"B站视频自动播放器"

2. **配置参数**
   - **搜索关键词**：输入要搜索的内容（默认：mcp技术）
   - **播放时长**：每个视频播放多少秒（10-300秒）
   - **最大播放数量**：总共播放多少个视频（1-20个）
   - **视频间隔**：视频之间等待多少秒（1-10秒）

3. **开始播放**
   - 点击"开始播放"按钮
   - 扩展会自动：
     - 打开B站搜索页面
     - 搜索指定关键词
     - 提取视频链接
     - 依次播放视频

4. **监控进度**
   - 实时查看播放状态
   - 查看当前播放的视频
   - 查看播放进度和记录

5. **停止播放**
   - 点击"停止播放"按钮随时停止

### 高级功能

- **播放记录**：查看已播放视频的详细信息
- **统计信息**：查看成功/失败播放数量
- **设置保存**：配置会自动保存，下次使用时恢复
- **错误处理**：播放失败时自动跳转到下一个视频

## 🔧 故障排除

### 常见问题

1. **扩展无法加载**
   ```
   解决方案：
   - 检查是否已生成图标文件
   - 确保manifest.json语法正确
   - 查看扩展管理页面的错误信息
   ```

2. **无法播放视频**
   ```
   解决方案：
   - 确保网络连接正常
   - 检查B站是否可以正常访问
   - 尝试手动播放一个B站视频
   - 查看浏览器控制台错误信息
   ```

3. **搜索结果为空**
   ```
   解决方案：
   - 检查搜索关键词是否有效
   - 尝试在B站手动搜索该关键词
   - 确认搜索页面正常加载
   ```

4. **播放按钮无效**
   ```
   解决方案：
   - 刷新页面重试
   - 检查是否有广告拦截器干扰
   - 尝试手动点击播放按钮
   ```

### 调试方法

1. **查看扩展日志**
   ```
   - 打开 chrome://extensions/
   - 找到"B站视频自动播放器"
   - 点击"检查视图"中的"background.html"或"service worker"
   - 查看Console标签页的日志
   ```

2. **查看页面日志**
   ```
   - 在B站页面按F12打开开发者工具
   - 查看Console标签页的输出
   - 查看Network标签页的网络请求
   ```

3. **检查权限**
   ```
   - 确保扩展有访问bilibili.com的权限
   - 检查是否被其他扩展或安全软件阻止
   ```

## ⚙️ 配置说明

### 权限说明
- `activeTab`：访问当前活动标签页
- `tabs`：创建和管理标签页  
- `storage`：保存设置和播放记录
- `scripting`：向页面注入脚本
- `https://*.bilibili.com/*`：访问B站所有页面

### 存储数据
扩展会在本地存储以下数据：
- 用户设置（搜索关键词、播放时长等）
- 播放记录（视频标题、播放状态等）

### 安全说明
- 扩展只访问B站域名
- 不收集用户个人信息
- 所有数据仅存储在本地

## 🔄 更新扩展

1. **手动更新**
   - 修改代码后，在扩展管理页面点击刷新按钮
   - 或者重新加载扩展文件夹

2. **版本管理**
   - 修改 `manifest.json` 中的版本号
   - 保持向后兼容性

## 📝 开发说明

### 文件结构
```
bilibili-auto-player/
├── manifest.json      # 扩展配置
├── popup.html         # 弹出页面
├── popup.css          # 样式文件
├── popup.js           # 弹出页面逻辑
├── background.js      # 后台服务
├── content.js         # 内容脚本
├── injected.js        # 注入脚本
└── icons/             # 图标文件夹
```

### 技术栈
- Manifest V3
- Chrome Extension APIs
- Vanilla JavaScript
- CSS3

### 扩展开发
如需修改功能，请参考：
- [Chrome扩展开发文档](https://developer.chrome.com/docs/extensions/)
- [Manifest V3指南](https://developer.chrome.com/docs/extensions/mv3/)

## 🆘 获取帮助

如果遇到问题：
1. 查看本文档的故障排除部分
2. 检查浏览器控制台错误信息
3. 确认Chrome版本支持Manifest V3
4. 尝试重新安装扩展

## ⚠️ 注意事项

- 请合理使用，避免对B站服务器造成过大负担
- 遵守B站的使用条款和相关法律法规
- 本扩展仅供学习和研究使用
- 使用本扩展产生的任何问题由用户自行承担
