# B站视频自动播放器 Chrome扩展

一个功能强大的Chrome扩展，可以自动搜索并播放B站视频，支持自定义搜索关键词、播放时长等参数。

## 🚀 功能特色

- ✨ **自动搜索**: 根据关键词自动搜索B站视频
- 🎬 **自动播放**: 依次播放搜索结果中的视频
- ⏱️ **自定义时长**: 可设置每个视频的播放时间（10-300秒）
- 📊 **播放记录**: 记录视频名称、播放时间、完成状态
- 🎛️ **灵活控制**: 可设置最大播放数量、视频间隔时间
- 📱 **实时状态**: Popup页面实时显示播放状态和进度
- 🔄 **智能切换**: 自动关闭已播放的标签页，打开新视频

## 📁 项目结构

```
bilibili-auto-player/
├── manifest.json          # 扩展配置文件
├── popup.html             # 弹出页面HTML
├── popup.css              # 弹出页面样式
├── popup.js               # 弹出页面逻辑
├── background.js          # 后台服务脚本
├── content.js             # 内容脚本
├── icons/                 # 图标文件夹
│   ├── icon16.png         # 16x16 图标
│   ├── icon32.png         # 32x32 图标
│   ├── icon48.png         # 48x48 图标
│   └── icon128.png        # 128x128 图标
└── README.md              # 说明文档
```

## 🛠️ 安装方法

### 方法1: 开发者模式安装

1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 `bilibili-auto-player` 文件夹
6. 扩展安装完成！

### 方法2: 打包安装

1. 在扩展管理页面点击"打包扩展程序"
2. 选择 `bilibili-auto-player` 文件夹
3. 生成 `.crx` 文件
4. 拖拽 `.crx` 文件到Chrome进行安装

## 📋 使用说明

### 基本操作

1. **设置参数**:
   - 搜索关键词: 默认"mcp技术"，可自定义
   - 播放时长: 每个视频播放秒数（10-300秒）
   - 最大播放数量: 总共播放多少个视频（1-20个）
   - 视频间隔: 视频之间的等待时间（1-10秒）

2. **开始播放**:
   - 点击"开始播放"按钮
   - 扩展会自动打开B站搜索页面
   - 自动提取视频链接并依次播放

3. **监控状态**:
   - 实时查看当前播放状态
   - 查看播放进度和记录
   - 统计成功/失败数量

4. **停止播放**:
   - 点击"停止播放"按钮
   - 清除播放记录

### 高级功能

- **智能播放**: 自动检测视频元素并开始播放
- **错误处理**: 播放失败时自动跳转到下一个视频
- **数据持久化**: 设置和记录自动保存到本地存储
- **防干扰**: 避免点击页面框架中的其他元素

## 🔧 技术实现

### 核心技术

- **Manifest V3**: 使用最新的Chrome扩展API
- **Service Worker**: 后台任务处理
- **Content Scripts**: 页面内容操作
- **Chrome Storage API**: 数据持久化
- **Chrome Tabs API**: 标签页管理

### 关键特性

- **模拟用户操作**: 通过真实的点击和播放操作避免反爬检测
- **异步处理**: 使用Promise和async/await处理异步操作
- **错误恢复**: 完善的错误处理和重试机制
- **内存管理**: 及时清理无用的标签页和资源

## 🎨 图标生成

如果需要重新生成图标，可以使用以下方法：

### 方法1: 使用HTML生成器
打开 `icons/create_simple_icons.html` 文件，点击生成按钮下载PNG图标。

### 方法2: 在线工具
1. 访问 [favicon.io](https://favicon.io/) 或类似工具
2. 上传设计图或使用文字生成
3. 下载16x16, 32x32, 48x48, 128x128尺寸的PNG文件
4. 重命名为 `icon16.png`, `icon32.png`, `icon48.png`, `icon128.png`
5. 放入 `icons/` 文件夹

## ⚙️ 配置说明

### 默认设置
```javascript
{
    searchKeyword: 'mcp技术',    // 搜索关键词
    playDuration: 30,            // 播放时长(秒)
    maxVideos: 5,                // 最大播放数量
    delayBetween: 2              // 视频间隔(秒)
}
```

### 权限说明
- `activeTab`: 访问当前活动标签页
- `tabs`: 创建和管理标签页
- `storage`: 保存设置和记录
- `scripting`: 注入脚本到页面
- `https://*.bilibili.com/*`: 访问B站域名

## 🐛 故障排除

### 常见问题

1. **扩展无法加载**
   - 检查manifest.json语法是否正确
   - 确保所有文件路径正确
   - 查看Chrome扩展管理页面的错误信息

2. **无法播放视频**
   - 检查B站页面是否正常加载
   - 确认网络连接正常
   - 查看浏览器控制台错误信息

3. **搜索结果为空**
   - 检查搜索关键词是否有效
   - 确认B站搜索页面结构未发生变化
   - 尝试手动访问搜索页面

### 调试方法

1. **查看后台日志**:
   - 打开 `chrome://extensions/`
   - 点击扩展的"检查视图"
   - 查看Console输出

2. **查看内容脚本日志**:
   - 在B站页面按F12打开开发者工具
   - 查看Console标签页的输出

## 📄 许可证

本项目仅供学习和研究使用，请遵守相关网站的使用条款。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## ⚠️ 免责声明

- 本扩展仅用于技术学习和研究
- 请合理使用，避免对B站服务器造成过大负担
- 使用本扩展产生的任何问题由用户自行承担
