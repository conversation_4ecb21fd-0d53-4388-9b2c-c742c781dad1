<!DOCTYPE html>
<html>
<head>
    <title>生成Chrome扩展图标</title>
</head>
<body>
    <h1>Chrome扩展图标生成器</h1>
    <p>点击按钮生成并下载图标文件</p>
    
    <div id="canvases"></div>
    <button onclick="generateAndDownloadAll()">生成并下载所有图标</button>
    
    <script>
        function createIcon(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // 背景渐变
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#00a1d6');
            gradient.addColorStop(1, '#0084c7');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // 播放按钮
            ctx.fillStyle = 'white';
            ctx.beginPath();
            const centerX = size / 2;
            const centerY = size / 2;
            const triangleSize = size * 0.25;
            
            ctx.moveTo(centerX - triangleSize * 0.5, centerY - triangleSize * 0.7);
            ctx.lineTo(centerX - triangleSize * 0.5, centerY + triangleSize * 0.7);
            ctx.lineTo(centerX + triangleSize * 0.8, centerY);
            ctx.closePath();
            ctx.fill();
            
            // 装饰圆环
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.6)';
            ctx.lineWidth = Math.max(1, size * 0.03);
            ctx.beginPath();
            ctx.arc(centerX, centerY, size * 0.35, 0, Math.PI * 2);
            ctx.stroke();
            
            return canvas;
        }
        
        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
        
        function generateAndDownloadAll() {
            const sizes = [16, 32, 48, 128];
            const container = document.getElementById('canvases');
            container.innerHTML = '';
            
            sizes.forEach(size => {
                const canvas = createIcon(size);
                canvas.style.margin = '10px';
                canvas.style.border = '1px solid #ccc';
                container.appendChild(canvas);
                
                // 自动下载
                setTimeout(() => {
                    downloadCanvas(canvas, `icon${size}.png`);
                }, 100 * size); // 延迟下载避免浏览器阻止
            });
            
            alert('图标生成完成！请将下载的PNG文件放入icons文件夹中。');
        }
    </script>
</body>
</html>
