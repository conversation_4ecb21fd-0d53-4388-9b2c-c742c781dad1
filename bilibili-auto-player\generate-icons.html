<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生成图标</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .icon-container {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        .icon-item {
            text-align: center;
            background: white;
            padding: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        canvas {
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .download-btn {
            margin-top: 10px;
            padding: 8px 16px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .download-btn:hover {
            background: #005a87;
        }
    </style>
</head>
<body>
    <h1>B站自动播放器图标生成器</h1>
    <p>点击下载按钮保存对应尺寸的图标到 icons 文件夹</p>
    
    <div class="icon-container">
        <div class="icon-item">
            <canvas id="icon16" width="16" height="16"></canvas>
            <div>16x16</div>
            <button class="download-btn" onclick="downloadIcon('icon16', 'icon16.png')">下载</button>
        </div>
        
        <div class="icon-item">
            <canvas id="icon32" width="32" height="32"></canvas>
            <div>32x32</div>
            <button class="download-btn" onclick="downloadIcon('icon32', 'icon32.png')">下载</button>
        </div>
        
        <div class="icon-item">
            <canvas id="icon48" width="48" height="48"></canvas>
            <div>48x48</div>
            <button class="download-btn" onclick="downloadIcon('icon48', 'icon48.png')">下载</button>
        </div>
        
        <div class="icon-item">
            <canvas id="icon128" width="128" height="128"></canvas>
            <div>128x128</div>
            <button class="download-btn" onclick="downloadIcon('icon128', 'icon128.png')">下载</button>
        </div>
    </div>
    
    <script>
        function drawIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            const scale = size / 128; // 基于128px设计
            
            // 清除画布
            ctx.clearRect(0, 0, size, size);
            
            // 背景渐变
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#00a1d6');
            gradient.addColorStop(1, '#0084c7');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // 播放按钮三角形
            ctx.fillStyle = 'white';
            ctx.beginPath();
            const centerX = size / 2;
            const centerY = size / 2;
            const triangleSize = size * 0.3;
            
            ctx.moveTo(centerX - triangleSize * 0.3, centerY - triangleSize * 0.5);
            ctx.lineTo(centerX - triangleSize * 0.3, centerY + triangleSize * 0.5);
            ctx.lineTo(centerX + triangleSize * 0.5, centerY);
            ctx.closePath();
            ctx.fill();
            
            // B站logo风格的装饰
            ctx.strokeStyle = 'white';
            ctx.lineWidth = Math.max(1, size * 0.02);
            ctx.beginPath();
            ctx.arc(centerX, centerY, size * 0.4, 0, Math.PI * 2);
            ctx.stroke();
            
            // 小圆点装饰
            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            const dotSize = size * 0.03;
            ctx.beginPath();
            ctx.arc(centerX + size * 0.25, centerY - size * 0.25, dotSize, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.beginPath();
            ctx.arc(centerX - size * 0.25, centerY + size * 0.25, dotSize, 0, Math.PI * 2);
            ctx.fill();
        }
        
        function downloadIcon(canvasId, filename) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // 生成所有尺寸的图标
        function generateAllIcons() {
            const sizes = [16, 32, 48, 128];
            sizes.forEach(size => {
                const canvas = document.getElementById(`icon${size}`);
                drawIcon(canvas, size);
            });
        }
        
        // 页面加载完成后生成图标
        window.addEventListener('load', generateAllIcons);
    </script>
</body>
</html>
