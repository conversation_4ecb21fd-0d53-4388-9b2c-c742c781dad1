#!/usr/bin/env python3
"""
生成Chrome扩展所需的PNG图标
"""

from PIL import Image, ImageDraw
import os

def create_icon(size):
    """创建指定尺寸的图标"""
    # 创建图像
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 背景渐变色（B站蓝色）
    # 简化为单色背景
    bg_color = (0, 161, 214, 255)  # B站蓝色
    draw.rectangle([0, 0, size, size], fill=bg_color)
    
    # 圆角效果（简化）
    corner_radius = max(2, size // 8)
    
    # 播放按钮三角形
    triangle_size = size * 0.3
    center_x, center_y = size // 2, size // 2
    
    # 三角形坐标
    triangle_points = [
        (center_x - triangle_size * 0.3, center_y - triangle_size * 0.5),
        (center_x - triangle_size * 0.3, center_y + triangle_size * 0.5),
        (center_x + triangle_size * 0.5, center_y)
    ]
    
    draw.polygon(triangle_points, fill=(255, 255, 255, 230))
    
    # 装饰圆环
    circle_radius = size * 0.35
    circle_width = max(1, size // 20)
    draw.ellipse([
        center_x - circle_radius, center_y - circle_radius,
        center_x + circle_radius, center_y + circle_radius
    ], outline=(255, 255, 255, 150), width=circle_width)
    
    # 装饰小点
    dot_radius = max(1, size // 25)
    # 右上角点
    draw.ellipse([
        size * 0.75 - dot_radius, size * 0.25 - dot_radius,
        size * 0.75 + dot_radius, size * 0.25 + dot_radius
    ], fill=(255, 255, 255, 200))
    
    # 左下角点
    draw.ellipse([
        size * 0.25 - dot_radius, size * 0.75 - dot_radius,
        size * 0.25 + dot_radius, size * 0.75 + dot_radius
    ], fill=(255, 255, 255, 200))
    
    return img

def main():
    """生成所有尺寸的图标"""
    sizes = [16, 32, 48, 128]
    
    # 确保icons目录存在
    icons_dir = 'icons'
    if not os.path.exists(icons_dir):
        os.makedirs(icons_dir)
    
    for size in sizes:
        print(f"生成 {size}x{size} 图标...")
        icon = create_icon(size)
        filename = os.path.join(icons_dir, f'icon{size}.png')
        icon.save(filename, 'PNG')
        print(f"保存: {filename}")
    
    print("所有图标生成完成！")

if __name__ == "__main__":
    main()
