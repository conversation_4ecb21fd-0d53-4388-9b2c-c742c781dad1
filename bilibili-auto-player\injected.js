/**
 * 注入到页面的脚本
 * 用于更深层次的页面操作和事件监听
 */

(function() {
    'use strict';
    
    class BilibiliPageInjector {
        constructor() {
            this.videoElement = null;
            this.isPlaying = false;
            this.playStartTime = null;
            this.observers = [];
            
            this.init();
        }
        
        init() {
            console.log('B站页面注入脚本初始化');
            
            // 等待页面完全加载
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.setupPage());
            } else {
                this.setupPage();
            }
            
            // 监听页面变化
            this.observePageChanges();
            
            // 设置消息监听
            this.setupMessageListener();
        }
        
        setupPage() {
            // 检测页面类型并初始化
            const url = window.location.href;
            
            if (url.includes('/video/')) {
                this.setupVideoPage();
            } else if (url.includes('search.bilibili.com')) {
                this.setupSearchPage();
            }
        }
        
        setupVideoPage() {
            console.log('设置视频页面');
            
            // 查找视频元素
            this.findVideoElement();
            
            // 设置视频控制
            this.setupVideoControls();
            
            // 监听视频事件
            this.setupVideoEvents();
        }
        
        setupSearchPage() {
            console.log('设置搜索页面');
            
            // 等待搜索结果加载
            this.waitForSearchResults();
        }
        
        findVideoElement() {
            const selectors = [
                'video',
                '.bilibili-player-video video',
                '.bpx-player-video-wrap video',
                '.player-wrap video'
            ];
            
            for (const selector of selectors) {
                const element = document.querySelector(selector);
                if (element) {
                    this.videoElement = element;
                    console.log('找到视频元素:', selector);
                    break;
                }
            }
            
            if (!this.videoElement) {
                // 延迟重试
                setTimeout(() => this.findVideoElement(), 1000);
            }
        }
        
        setupVideoControls() {
            if (!this.videoElement) return;
            
            // 设置视频属性
            this.videoElement.volume = 0.1; // 降低音量
            this.videoElement.playbackRate = 1.0; // 正常播放速度
            
            // 尝试移除广告和弹幕
            this.removeAdsAndDanmaku();
        }
        
        setupVideoEvents() {
            if (!this.videoElement) return;
            
            this.videoElement.addEventListener('loadeddata', () => {
                console.log('视频数据加载完成');
                this.notifyExtension('videoLoaded', {
                    duration: this.videoElement.duration,
                    title: this.getVideoTitle()
                });
            });
            
            this.videoElement.addEventListener('play', () => {
                console.log('视频开始播放');
                this.isPlaying = true;
                this.playStartTime = Date.now();
                this.notifyExtension('videoPlay', {
                    title: this.getVideoTitle()
                });
            });
            
            this.videoElement.addEventListener('pause', () => {
                console.log('视频暂停');
                this.isPlaying = false;
            });
            
            this.videoElement.addEventListener('ended', () => {
                console.log('视频播放结束');
                this.isPlaying = false;
                this.notifyExtension('videoEnded', {
                    title: this.getVideoTitle(),
                    playedDuration: this.getPlayedDuration()
                });
            });
            
            this.videoElement.addEventListener('error', (error) => {
                console.error('视频播放错误:', error);
                this.notifyExtension('videoError', {
                    title: this.getVideoTitle(),
                    error: error.message || '播放错误'
                });
            });
        }
        
        removeAdsAndDanmaku() {
            // 移除广告元素
            const adSelectors = [
                '.bilibili-player-video-toast',
                '.bilibili-player-video-toast-bottom',
                '.bilibili-player-video-toast-top',
                '.bpx-player-toast',
                '.ad-report',
                '.advertisement'
            ];
            
            adSelectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(el => el.remove());
            });
            
            // 关闭弹幕
            setTimeout(() => {
                const danmakuButton = document.querySelector('.bpx-player-ctrl-subtitle, .bilibili-player-video-danmaku-switch');
                if (danmakuButton && danmakuButton.classList.contains('bpx-state-active')) {
                    danmakuButton.click();
                    console.log('关闭弹幕');
                }
            }, 2000);
        }
        
        waitForSearchResults() {
            const checkResults = () => {
                const videoCards = document.querySelectorAll('.bili-video-card, .video-item, .bili-video-card__wrap');
                
                if (videoCards.length > 0) {
                    console.log(`搜索结果加载完成，找到 ${videoCards.length} 个视频`);
                    this.notifyExtension('searchResultsReady', {
                        count: videoCards.length
                    });
                } else {
                    setTimeout(checkResults, 1000);
                }
            };
            
            setTimeout(checkResults, 2000);
        }
        
        observePageChanges() {
            // 监听DOM变化
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList') {
                        // 检查是否有新的视频元素
                        if (!this.videoElement && window.location.href.includes('/video/')) {
                            this.findVideoElement();
                        }
                    }
                });
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
            
            this.observers.push(observer);
        }
        
        setupMessageListener() {
            // 监听来自content script的消息
            window.addEventListener('message', (event) => {
                if (event.source !== window) return;
                
                const { type, data } = event.data;
                
                switch (type) {
                    case 'BILIBILI_AUTO_PLAY':
                        this.handleAutoPlay();
                        break;
                    case 'BILIBILI_AUTO_PAUSE':
                        this.handleAutoPause();
                        break;
                    case 'BILIBILI_GET_INFO':
                        this.sendVideoInfo();
                        break;
                }
            });
        }
        
        handleAutoPlay() {
            if (this.videoElement) {
                this.videoElement.play().then(() => {
                    console.log('自动播放成功');
                }).catch(error => {
                    console.error('自动播放失败:', error);
                    // 尝试点击播放按钮
                    this.clickPlayButton();
                });
            } else {
                this.clickPlayButton();
            }
        }
        
        handleAutoPause() {
            if (this.videoElement) {
                this.videoElement.pause();
            }
        }
        
        clickPlayButton() {
            const playButtonSelectors = [
                '.bpx-player-ctrl-play',
                '.bilibili-player-video-btn-start',
                '.bpx-player-ctrl-btn[aria-label="播放"]',
                '.player-start-btn'
            ];
            
            for (const selector of playButtonSelectors) {
                const button = document.querySelector(selector);
                if (button && button.offsetParent !== null) {
                    console.log('点击播放按钮:', selector);
                    button.click();
                    break;
                }
            }
        }
        
        sendVideoInfo() {
            const info = {
                title: this.getVideoTitle(),
                duration: this.videoElement?.duration || 0,
                currentTime: this.videoElement?.currentTime || 0,
                isPlaying: this.isPlaying,
                playedDuration: this.getPlayedDuration()
            };
            
            window.postMessage({
                type: 'BILIBILI_VIDEO_INFO',
                data: info
            }, '*');
        }
        
        getVideoTitle() {
            const titleSelectors = [
                'h1[title]',
                '.video-title',
                '.media-title',
                '.bpx-player-top-title',
                '.bilibili-player-video-top-title'
            ];
            
            for (const selector of titleSelectors) {
                const element = document.querySelector(selector);
                if (element) {
                    return element.textContent?.trim() || element.title || '未知标题';
                }
            }
            
            return document.title || '未知标题';
        }
        
        getPlayedDuration() {
            if (!this.playStartTime) return 0;
            return Math.floor((Date.now() - this.playStartTime) / 1000);
        }
        
        notifyExtension(event, data) {
            // 通过postMessage发送消息给content script
            window.postMessage({
                type: 'BILIBILI_EXTENSION_EVENT',
                event: event,
                data: data
            }, '*');
        }
        
        destroy() {
            // 清理观察者
            this.observers.forEach(observer => observer.disconnect());
            this.observers = [];
        }
    }
    
    // 初始化注入脚本
    if (!window.bilibiliPageInjector) {
        window.bilibiliPageInjector = new BilibiliPageInjector();
    }
    
})();
