<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中文显示测试</title>
    <style>
        body {
            font-family: <PERSON><PERSON>, "Microsoft YaHei", "SimHei", sans-serif;
            background: #000;
            color: #fff;
            text-align: center;
            padding: 50px;
        }
        .test-text {
            font-size: 48px;
            margin: 20px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body>
    <h1>中文显示测试页面</h1>
    <div class="test-text">邱大山你真棒！</div>
    <p>如果你能看到上面的中文文字"邱大山你真棒！"，说明中文显示正常。</p>
    <p><a href="index.html" style="color: #4ecdc4;">返回3D效果页面</a></p>
    
    <script>
        console.log('测试文字: 邱大山你真棒！');
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，中文文字应该正常显示');
        });
    </script>
</body>
</html>
