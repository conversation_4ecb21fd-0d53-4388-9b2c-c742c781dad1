* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 400px;
    min-height: 600px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
}

.container {
    padding: 16px;
    height: 100%;
}

.header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    background: rgba(255, 255, 255, 0.1);
    padding: 12px;
    border-radius: 8px;
    backdrop-filter: blur(10px);
}

.logo {
    width: 32px;
    height: 32px;
    margin-right: 12px;
}

.header h1 {
    font-size: 18px;
    color: white;
    font-weight: 600;
}

.settings {
    background: rgba(255, 255, 255, 0.95);
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 16px;
}

.setting-group {
    margin-bottom: 12px;
}

.setting-group:last-child {
    margin-bottom: 0;
}

.setting-group label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 4px;
    color: #555;
}

.setting-group input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s;
}

.setting-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.controls {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
}

.btn {
    flex: 1;
    padding: 10px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.btn-secondary {
    background: linear-gradient(45deg, #f44336, #d32f2f);
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
}

.btn-warning {
    background: linear-gradient(45deg, #ff9800, #f57c00);
    color: white;
}

.btn-warning:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3);
}

.status {
    background: rgba(255, 255, 255, 0.95);
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 16px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 6px;
    font-size: 14px;
}

.status-item:last-child {
    margin-bottom: 0;
}

.status-item .label {
    font-weight: 500;
    color: #666;
}

.status-item .value {
    font-weight: 600;
    color: #333;
}

.video-list {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 16px;
    max-height: 200px;
    overflow-y: auto;
}

.video-list h3 {
    font-size: 16px;
    margin-bottom: 12px;
    color: #333;
    border-bottom: 2px solid #eee;
    padding-bottom: 6px;
}

.records {
    max-height: 150px;
    overflow-y: auto;
}

.no-records {
    text-align: center;
    color: #999;
    font-size: 14px;
    padding: 20px;
}

.record-item {
    padding: 8px;
    border-bottom: 1px solid #eee;
    font-size: 12px;
}

.record-item:last-child {
    border-bottom: none;
}

.record-title {
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.record-info {
    display: flex;
    justify-content: space-between;
    color: #666;
    font-size: 11px;
}

.record-status {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 500;
}

.status-success {
    background: #e8f5e8;
    color: #4caf50;
}

.status-failed {
    background: #ffebee;
    color: #f44336;
}

.status-playing {
    background: #e3f2fd;
    color: #2196f3;
}

.footer {
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 12px;
    border-radius: 6px;
    backdrop-filter: blur(10px);
}

.stats {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: white;
    font-weight: 500;
}

/* 滚动条样式 */
.records::-webkit-scrollbar {
    width: 4px;
}

.records::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

.records::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

.records::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
