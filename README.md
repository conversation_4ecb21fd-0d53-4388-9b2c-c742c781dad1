# 🎉 3D酷炫文字效果 - "邱大山你真棒！"

一个使用Three.js创建的酷炫3D文字展示项目，具有多种动画效果和交互功能。

## ✨ 特色功能

### 🎨 视觉效果
- **3D立体文字**: 使用Three.js TextGeometry创建立体文字"邱大山你真棒！"
- **渐变材质**: 动态彩虹渐变色彩，支持多种颜色主题切换
- **粒子背景**: 1000个彩色粒子营造梦幻背景效果
- **专业灯光**: 环境光、方向光、点光源、聚光灯组合照明
- **阴影效果**: 真实的阴影渲染增强立体感

### 🎭 动画效果
- **自动旋转**: 文字沿X、Y轴平滑旋转
- **浮动动画**: 基于正弦波的上下浮动效果
- **缩放脉冲**: 文字大小的周期性变化
- **粒子流动**: 背景粒子的动态流动效果

### 🎮 交互控制
- **鼠标控制**: 拖拽旋转视角，滚轮缩放
- **动画开关**: 一键开启/关闭所有动画效果
- **颜色切换**: 4种不同的渐变色彩主题
- **粒子控制**: 显示/隐藏粒子背景效果

## 🚀 快速开始

### 方法1: 使用Python服务器（推荐）
```bash
# 运行内置服务器
python server.py
```

### 方法2: 使用Python内置服务器
```bash
# Python 3
python -m http.server 8000

# 然后访问 http://localhost:8000
```

### 方法3: 使用Node.js服务器
```bash
# 安装http-server
npm install -g http-server

# 启动服务器
http-server -p 8000
```

## 🎯 操作指南

### 鼠标操作
- **左键拖拽**: 旋转视角
- **滚轮**: 缩放视图
- **右键拖拽**: 平移视图

### 控制按钮
- **切换动画**: 开启/关闭自动动画效果
- **变换颜色**: 切换4种渐变色彩主题
- **粒子效果**: 显示/隐藏背景粒子

## 🛠️ 技术栈

- **Three.js**: 3D图形渲染引擎
- **WebGL**: 硬件加速的3D图形
- **HTML5 Canvas**: 渐变纹理生成
- **ES6+**: 现代JavaScript语法

## 📁 项目结构

```
├── index.html          # 主页面文件
├── script.js           # 核心JavaScript代码
├── server.py           # Python本地服务器
└── README.md           # 项目说明文档
```

## 🎨 颜色主题

1. **热情红橙**: 红色到青色的渐变
2. **梦幻紫蓝**: 紫色到蓝色的渐变  
3. **清新蓝绿**: 蓝色到绿色的渐变
4. **温暖粉黄**: 粉色到黄色的渐变

## 🔧 自定义配置

### 修改文字内容
在 `script.js` 中找到以下代码并修改：
```javascript
const textGeometry = new THREE.TextGeometry('邱大山你真棒！', {
    // 配置参数...
});
```

### 调整动画速度
修改动画函数中的时间系数：
```javascript
textMesh.rotation.y += 0.01;  // 调整旋转速度
textMesh.position.y = Math.sin(Date.now() * 0.002) * 2;  // 调整浮动速度
```

### 更改粒子数量
修改粒子系统配置：
```javascript
const particleCount = 1000;  // 调整粒子数量
```

## 🌟 效果预览

- 立体3D文字效果
- 动态彩虹渐变材质
- 平滑的旋转和浮动动画
- 梦幻粒子背景
- 响应式交互控制

## 📱 兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+
- ⚠️ 需要支持WebGL的现代浏览器

## 🎊 享受酷炫的3D文字效果吧！

这个项目展示了"邱大山你真棒！"的3D文字，配合多种动画效果和交互功能，创造出令人印象深刻的视觉体验。
