<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>腾讯云开发快速部署指南</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        h1, h2 {
            color: #fff;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .step {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border-left: 4px solid #4ecdc4;
        }
        .code {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .highlight {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            padding: 2px 8px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
        }
        .btn {
            display: inline-block;
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 25px;
            margin: 10px 5px;
            transition: transform 0.2s;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }
        .warning {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid #ffc107;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 腾讯云开发快速部署指南</h1>
        <p>将您的 <span class="highlight">"邱大山你真棒！"</span> 3D文字效果部署到云端！</p>

        <div class="step">
            <h2>📋 第一步：准备工作</h2>
            <p>1. 确保您有腾讯云账号</p>
            <p>2. 访问 <a href="https://console.cloud.tencent.com/tcb" target="_blank" class="btn">腾讯云开发控制台</a></p>
            <p>3. 创建新的云开发环境（如果还没有）</p>
        </div>

        <div class="step">
            <h2>🔧 第二步：安装CLI工具</h2>
            <p>在命令行中运行：</p>
            <div class="code">npm install -g @cloudbase/cli</div>
            <div class="warning">
                <strong>注意：</strong> 安装可能需要几分钟时间，请耐心等待。
            </div>
        </div>

        <div class="step">
            <h2>🔑 第三步：登录云开发</h2>
            <p>运行登录命令：</p>
            <div class="code">cloudbase login</div>
            <p>这将打开浏览器，使用您的腾讯云账号登录。</p>
        </div>

        <div class="step">
            <h2>⚙️ 第四步：配置环境</h2>
            <p>1. 在云开发控制台获取您的环境ID</p>
            <p>2. 修改 <code>cloudbaserc.json</code> 文件</p>
            <p>3. 将 <code>{{env.ENV_ID}}</code> 替换为您的实际环境ID</p>
        </div>

        <div class="step">
            <h2>🚀 第五步：部署项目</h2>
            <p>运行部署命令：</p>
            <div class="code">cloudbase framework deploy</div>
            <p>等待部署完成，您将获得访问链接！</p>
        </div>

        <div class="step">
            <h2>🎯 项目特色</h2>
            <ul>
                <li>✨ 3D立体文字："邱大山你真棒！"</li>
                <li>🎨 动态渐变色彩和粒子效果</li>
                <li>🎭 旋转、浮动、缩放动画</li>
                <li>🎮 鼠标交互和按钮控制</li>
                <li>📱 响应式设计，支持移动端</li>
            </ul>
        </div>

        <div class="step">
            <h2>🌐 部署后的优势</h2>
            <ul>
                <li>🔒 HTTPS安全访问</li>
                <li>⚡ CDN全球加速</li>
                <li>📊 访问统计分析</li>
                <li>🔧 在线管理和更新</li>
                <li>💰 按量付费，成本低廉</li>
            </ul>
        </div>

        <div class="warning">
            <h3>💡 小贴士</h3>
            <p>• 首次部署可能需要3-5分钟</p>
            <p>• 部署成功后会提供访问链接</p>
            <p>• 可以绑定自定义域名</p>
            <p>• 支持版本管理和回滚</p>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="index.html" class="btn">🎮 体验3D效果</a>
            <a href="test.html" class="btn">🔤 测试中文显示</a>
        </div>

        <div style="text-align: center; margin-top: 20px; opacity: 0.8;">
            <p>🎊 部署完成后，您的酷炫3D文字效果将在云端运行！</p>
        </div>
    </div>

    <script>
        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const steps = document.querySelectorAll('.step');
            steps.forEach((step, index) => {
                step.style.opacity = '0';
                step.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    step.style.transition = 'all 0.6s ease';
                    step.style.opacity = '1';
                    step.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
