#!/usr/bin/env python3
"""
简单的图标生成脚本
使用PIL库生成Chrome扩展所需的PNG图标
"""

try:
    from PIL import Image, ImageDraw
    import os
    
    def create_icon(size):
        """创建指定尺寸的图标"""
        # 创建透明背景图像
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # B站蓝色背景
        bg_color = (0, 161, 214, 255)
        draw.rectangle([0, 0, size, size], fill=bg_color)
        
        # 播放按钮三角形
        center_x, center_y = size // 2, size // 2
        triangle_size = size * 0.25
        
        triangle_points = [
            (center_x - triangle_size * 0.5, center_y - triangle_size * 0.7),
            (center_x - triangle_size * 0.5, center_y + triangle_size * 0.7),
            (center_x + triangle_size * 0.8, center_y)
        ]
        
        draw.polygon(triangle_points, fill=(255, 255, 255, 230))
        
        # 装饰圆环
        circle_radius = size * 0.35
        circle_bbox = [
            center_x - circle_radius, center_y - circle_radius,
            center_x + circle_radius, center_y + circle_radius
        ]
        draw.ellipse(circle_bbox, outline=(255, 255, 255, 150), width=max(1, size // 20))
        
        return img
    
    def main():
        """生成所有尺寸的图标"""
        sizes = [16, 32, 48, 128]
        
        # 确保icons目录存在
        icons_dir = 'icons'
        if not os.path.exists(icons_dir):
            os.makedirs(icons_dir)
        
        for size in sizes:
            print(f"生成 {size}x{size} 图标...")
            icon = create_icon(size)
            filename = os.path.join(icons_dir, f'icon{size}.png')
            icon.save(filename, 'PNG')
            print(f"保存: {filename}")
        
        print("所有图标生成完成！")
    
    if __name__ == "__main__":
        main()

except ImportError:
    print("PIL库未安装，请运行: pip install Pillow")
    print("或者使用 icons/create_simple_icons.html 在浏览器中生成图标")
