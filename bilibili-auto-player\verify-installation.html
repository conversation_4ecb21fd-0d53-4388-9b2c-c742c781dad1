<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>扩展安装验证</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 80vh;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            text-align: center;
        }
        
        h1 {
            margin-bottom: 30px;
            color: #fff;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .step {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            text-align: left;
        }
        
        .step h3 {
            color: #4ecdc4;
            margin-bottom: 15px;
        }
        
        .step-number {
            background: #4ecdc4;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: transform 0.2s;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #2196F3, #1976D2);
        }
        
        .btn-secondary:hover {
            box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
        }
        
        .warning {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid #ffc107;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .code {
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
        
        .checklist {
            list-style: none;
            padding: 0;
        }
        
        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .checklist li:before {
            content: "□ ";
            color: #ffc107;
            font-weight: bold;
            margin-right: 8px;
        }
        
        .checklist li.checked:before {
            content: "✓ ";
            color: #4CAF50;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Chrome扩展安装验证</h1>
        
        <div class="warning">
            <strong>重要提示：</strong>
            Chrome扩展API只能在扩展环境中使用，不能在普通网页中测试。
            请按照以下步骤正确安装和验证扩展。
        </div>
        
        <div class="step">
            <h3><span class="step-number">1</span>打开Chrome扩展管理页面</h3>
            <p>点击下面的按钮直接打开扩展管理页面：</p>
            <a href="chrome://extensions/" class="btn" target="_blank">打开扩展管理</a>
            <p>或者手动在地址栏输入：<span class="code">chrome://extensions/</span></p>
        </div>
        
        <div class="step">
            <h3><span class="step-number">2</span>开启开发者模式</h3>
            <p>在扩展管理页面右上角，确保"开发者模式"开关已开启（蓝色状态）。</p>
        </div>
        
        <div class="step">
            <h3><span class="step-number">3</span>加载扩展</h3>
            <p>点击"加载已解压的扩展程序"按钮，选择项目文件夹：</p>
            <div class="code">bilibili-auto-player</div>
            <p>确保选择的文件夹包含 manifest.json 文件。</p>
        </div>
        
        <div class="step">
            <h3><span class="step-number">4</span>验证安装</h3>
            <p>安装成功后，请检查以下项目：</p>
            <ul class="checklist">
                <li>扩展出现在扩展列表中</li>
                <li>扩展状态为"已启用"</li>
                <li>Chrome工具栏出现扩展图标</li>
                <li>点击图标能打开控制面板</li>
                <li>控制面板显示正常（蓝色渐变背景）</li>
            </ul>
        </div>
        
        <div class="step">
            <h3><span class="step-number">5</span>测试功能</h3>
            <p>如果扩展安装成功，您可以：</p>
            <ol>
                <li>点击扩展图标打开控制面板</li>
                <li>查看默认设置（搜索关键词：mcp技术）</li>
                <li>点击"开始播放"按钮</li>
                <li>观察是否自动打开B站搜索页面</li>
            </ol>
        </div>
        
        <div class="success">
            <h3>✅ 安装成功标志</h3>
            <p>当您看到以下情况时，说明扩展安装成功：</p>
            <ul>
                <li>扩展图标出现在Chrome工具栏</li>
                <li>点击图标显示蓝色渐变的控制面板</li>
                <li>面板中显示"状态: 待机中"</li>
                <li>所有设置选项可以正常修改</li>
                <li>点击"开始播放"后状态变为"运行中"</li>
            </ul>
        </div>
        
        <div class="step">
            <h3><span class="step-number">6</span>开始使用</h3>
            <p>扩展安装成功后，您就可以开始使用了：</p>
            <a href="https://www.bilibili.com" class="btn btn-secondary" target="_blank">打开B站测试</a>
            <p>在B站页面上，扩展会自动注入必要的脚本，准备进行自动播放。</p>
        </div>
        
        <div class="warning">
            <h3>⚠️ 常见问题</h3>
            <ul>
                <li><strong>图标文件错误：</strong>确保icons文件夹中有PNG格式的图标文件</li>
                <li><strong>权限不足：</strong>确保Chrome允许加载未打包的扩展</li>
                <li><strong>文件路径错误：</strong>确保选择了包含manifest.json的正确文件夹</li>
                <li><strong>浏览器版本：</strong>确保Chrome版本支持Manifest V3</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <p>如果按照以上步骤操作后扩展仍无法正常工作，请检查Chrome控制台的错误信息。</p>
            <a href="QUICK_START.md" class="btn">查看详细指南</a>
        </div>
    </div>
    
    <script>
        // 检查是否在扩展环境中
        if (typeof chrome !== 'undefined' && chrome.runtime) {
            document.body.innerHTML = `
                <div class="container">
                    <h1>🎉 扩展环境检测成功！</h1>
                    <div class="success">
                        <p>恭喜！您正在扩展环境中查看此页面，这意味着扩展已经正确安装。</p>
                        <p>现在您可以正常使用B站视频自动播放器的所有功能了！</p>
                    </div>
                </div>
            `;
        }
        
        // 添加点击效果
        document.addEventListener('DOMContentLoaded', function() {
            const checklistItems = document.querySelectorAll('.checklist li');
            checklistItems.forEach(item => {
                item.addEventListener('click', function() {
                    this.classList.toggle('checked');
                });
            });
        });
    </script>
</body>
</html>
