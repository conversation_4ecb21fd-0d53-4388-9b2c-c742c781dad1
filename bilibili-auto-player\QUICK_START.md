# 🚀 B站自动播放器 - 快速开始指南

## ⚡ 立即安装使用

### 第一步：安装Chrome扩展

1. **打开Chrome扩展管理页面**
   ```
   在地址栏输入：chrome://extensions/
   或者：Chrome菜单 → 更多工具 → 扩展程序
   ```

2. **开启开发者模式**
   - 点击右上角的"开发者模式"开关，确保已开启

3. **加载扩展**
   - 点击"加载已解压的扩展程序"按钮
   - 选择 `bilibili-auto-player` 文件夹
   - 点击"选择文件夹"

4. **确认安装成功**
   - 扩展应该出现在扩展列表中
   - 确保扩展已启用（开关为蓝色）
   - Chrome工具栏应该出现扩展图标

### 第二步：测试扩展功能

1. **打开扩展控制面板**
   - 点击Chrome工具栏中的扩展图标
   - 或者点击拼图图标，然后选择"B站视频自动播放器"

2. **验证界面显示**
   - 应该看到蓝色渐变的控制面板
   - 包含设置选项和控制按钮
   - 显示当前状态为"待机中"

3. **配置播放参数**
   - 搜索关键词：默认"mcp技术"（可修改）
   - 播放时长：30秒（可调整10-300秒）
   - 最大播放数量：5个（可调整1-20个）
   - 视频间隔：2秒（可调整1-10秒）

### 第三步：开始自动播放

1. **点击"开始播放"按钮**
   - 扩展会自动打开B站搜索页面
   - 搜索指定的关键词
   - 提取视频链接列表

2. **观察自动播放过程**
   - 扩展会依次打开视频页面
   - 自动开始播放视频
   - 播放指定时长后关闭标签页
   - 继续播放下一个视频

3. **监控播放状态**
   - 在控制面板中查看实时状态
   - 查看当前播放的视频名称
   - 查看播放进度和记录

## 🔍 故障排除

### 问题1：扩展无法加载
**症状**：点击"加载已解压的扩展程序"后出现错误

**解决方案**：
1. 检查是否选择了正确的文件夹（应该包含manifest.json）
2. 确保所有图标文件都存在（icons文件夹中的PNG文件）
3. 检查manifest.json语法是否正确

### 问题2：扩展图标不显示
**症状**：扩展安装成功但工具栏没有图标

**解决方案**：
1. 点击Chrome工具栏右侧的拼图图标
2. 找到"B站视频自动播放器"并点击固定图标
3. 刷新扩展或重启Chrome

### 问题3：Popup页面无法打开
**症状**：点击扩展图标没有反应

**解决方案**：
1. 在扩展管理页面点击"重新加载"
2. 检查popup.html文件是否存在
3. 查看扩展详情页面的错误信息

### 问题4：无法播放视频
**症状**：扩展运行但视频不播放

**解决方案**：
1. 确保网络连接正常
2. 手动访问B站确认可以正常播放视频
3. 检查浏览器是否阻止了自动播放
4. 在B站页面按F12查看控制台错误

## 🧪 调试方法

### 查看后台脚本日志
1. 打开 `chrome://extensions/`
2. 找到"B站视频自动播放器"
3. 点击"检查视图"中的"service worker"
4. 查看Console标签页的日志输出

### 查看内容脚本日志
1. 在B站页面按F12打开开发者工具
2. 查看Console标签页
3. 查找以"B站"开头的日志信息

### 检查扩展权限
1. 在扩展详情页面查看权限列表
2. 确保已授予访问bilibili.com的权限
3. 如有权限请求，点击"允许"

## ✅ 成功标志

当扩展正常工作时，您应该看到：

1. **控制面板正常显示**
   - 蓝色渐变背景
   - 所有设置选项可用
   - 按钮响应正常

2. **自动播放流程**
   - 自动打开B站搜索页面
   - 状态显示"运行中"
   - 视频依次自动播放
   - 播放记录实时更新

3. **日志输出正常**
   - 后台脚本有相关日志
   - B站页面控制台有扩展日志
   - 无错误信息

## 🎯 使用技巧

1. **首次使用建议**
   - 先设置较少的视频数量（如2-3个）进行测试
   - 播放时长设置短一些（如15-20秒）
   - 观察整个流程是否正常

2. **优化设置**
   - 根据网络速度调整视频间隔时间
   - 根据需要调整播放时长
   - 选择合适的搜索关键词

3. **监控使用**
   - 定期查看播放记录
   - 注意成功/失败统计
   - 及时停止异常运行

## 🎊 开始享受自动播放！

按照以上步骤，您就可以成功安装并使用B站视频自动播放器了。这个扩展会帮您自动搜索和播放视频，节省大量手动操作时间！

如果遇到任何问题，请按照故障排除部分的指导进行解决，或查看详细的调试日志信息。
