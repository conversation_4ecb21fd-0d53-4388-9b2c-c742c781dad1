class BilibiliAutoPlayer {
    constructor() {
        this.isRunning = false;
        this.currentVideoIndex = 0;
        this.videoRecords = [];
        this.settings = {
            searchKeyword: 'mcp技术',
            playDuration: 30,
            maxVideos: 5,
            delayBetween: 2
        };
        
        this.initElements();
        this.loadSettings();
        this.loadRecords();
        this.bindEvents();
        this.updateUI();
        this.startStatusPolling();
    }
    
    initElements() {
        this.elements = {
            searchKeyword: document.getElementById('searchKeyword'),
            playDuration: document.getElementById('playDuration'),
            maxVideos: document.getElementById('maxVideos'),
            delayBetween: document.getElementById('delayBetween'),
            startBtn: document.getElementById('startBtn'),
            stopBtn: document.getElementById('stopBtn'),
            clearBtn: document.getElementById('clearBtn'),
            currentStatus: document.getElementById('currentStatus'),
            currentVideo: document.getElementById('currentVideo'),
            progress: document.getElementById('progress'),
            videoRecords: document.getElementById('videoRecords'),
            totalPlayed: document.getElementById('totalPlayed'),
            successCount: document.getElementById('successCount'),
            failCount: document.getElementById('failCount')
        };
    }
    
    bindEvents() {
        this.elements.startBtn.addEventListener('click', () => this.startAutoPlay());
        this.elements.stopBtn.addEventListener('click', () => this.stopAutoPlay());
        this.elements.clearBtn.addEventListener('click', () => this.clearRecords());
        
        // 保存设置
        ['searchKeyword', 'playDuration', 'maxVideos', 'delayBetween'].forEach(key => {
            this.elements[key].addEventListener('change', () => this.saveSettings());
        });
    }
    
    async loadSettings() {
        try {
            const result = await chrome.storage.local.get(['bilibiliAutoPlayerSettings']);
            if (result.bilibiliAutoPlayerSettings) {
                this.settings = { ...this.settings, ...result.bilibiliAutoPlayerSettings };
                this.updateSettingsUI();
            }
        } catch (error) {
            console.error('加载设置失败:', error);
        }
    }
    
    async saveSettings() {
        this.settings = {
            searchKeyword: this.elements.searchKeyword.value,
            playDuration: parseInt(this.elements.playDuration.value),
            maxVideos: parseInt(this.elements.maxVideos.value),
            delayBetween: parseInt(this.elements.delayBetween.value)
        };
        
        try {
            await chrome.storage.local.set({ bilibiliAutoPlayerSettings: this.settings });
        } catch (error) {
            console.error('保存设置失败:', error);
        }
    }
    
    updateSettingsUI() {
        this.elements.searchKeyword.value = this.settings.searchKeyword;
        this.elements.playDuration.value = this.settings.playDuration;
        this.elements.maxVideos.value = this.settings.maxVideos;
        this.elements.delayBetween.value = this.settings.delayBetween;
    }
    
    async loadRecords() {
        try {
            const result = await chrome.storage.local.get(['bilibiliVideoRecords']);
            if (result.bilibiliVideoRecords) {
                this.videoRecords = result.bilibiliVideoRecords;
                this.updateRecordsUI();
            }
        } catch (error) {
            console.error('加载记录失败:', error);
        }
    }
    
    async saveRecords() {
        try {
            await chrome.storage.local.set({ bilibiliVideoRecords: this.videoRecords });
        } catch (error) {
            console.error('保存记录失败:', error);
        }
    }
    
    async startAutoPlay() {
        await this.saveSettings();
        
        this.isRunning = true;
        this.currentVideoIndex = 0;
        this.updateUI();
        
        // 发送消息给background script开始自动播放
        try {
            await chrome.runtime.sendMessage({
                action: 'startAutoPlay',
                settings: this.settings
            });
        } catch (error) {
            console.error('启动自动播放失败:', error);
            this.isRunning = false;
            this.updateUI();
        }
    }
    
    async stopAutoPlay() {
        this.isRunning = false;
        this.updateUI();
        
        try {
            await chrome.runtime.sendMessage({ action: 'stopAutoPlay' });
        } catch (error) {
            console.error('停止自动播放失败:', error);
        }
    }
    
    async clearRecords() {
        this.videoRecords = [];
        await this.saveRecords();
        this.updateRecordsUI();
    }
    
    updateUI() {
        this.elements.startBtn.disabled = this.isRunning;
        this.elements.stopBtn.disabled = !this.isRunning;
        
        if (this.isRunning) {
            this.elements.currentStatus.textContent = '运行中';
            this.elements.currentStatus.style.color = '#4caf50';
        } else {
            this.elements.currentStatus.textContent = '待机中';
            this.elements.currentStatus.style.color = '#666';
        }
        
        this.updateStatsUI();
    }
    
    updateRecordsUI() {
        const container = this.elements.videoRecords;
        
        if (this.videoRecords.length === 0) {
            container.innerHTML = '<div class="no-records">暂无播放记录</div>';
            return;
        }
        
        const recordsHTML = this.videoRecords.map(record => `
            <div class="record-item">
                <div class="record-title">${record.title}</div>
                <div class="record-info">
                    <span>播放时长: ${record.playedDuration}s</span>
                    <span class="record-status status-${record.status}">${this.getStatusText(record.status)}</span>
                </div>
            </div>
        `).join('');
        
        container.innerHTML = recordsHTML;
        this.updateStatsUI();
    }
    
    updateStatsUI() {
        const total = this.videoRecords.length;
        const success = this.videoRecords.filter(r => r.status === 'success').length;
        const failed = this.videoRecords.filter(r => r.status === 'failed').length;
        
        this.elements.totalPlayed.textContent = total;
        this.elements.successCount.textContent = success;
        this.elements.failCount.textContent = failed;
        this.elements.progress.textContent = `${this.currentVideoIndex}/${this.settings.maxVideos}`;
    }
    
    getStatusText(status) {
        const statusMap = {
            'success': '完成',
            'failed': '失败',
            'playing': '播放中'
        };
        return statusMap[status] || '未知';
    }
    
    startStatusPolling() {
        setInterval(async () => {
            try {
                const response = await chrome.runtime.sendMessage({ action: 'getStatus' });
                if (response) {
                    this.updateStatus(response);
                }
            } catch (error) {
                // 忽略连接错误
            }
        }, 1000);
    }
    
    updateStatus(status) {
        if (status.isRunning !== undefined) {
            this.isRunning = status.isRunning;
        }
        
        if (status.currentVideo) {
            this.elements.currentVideo.textContent = status.currentVideo;
        }
        
        if (status.currentIndex !== undefined) {
            this.currentVideoIndex = status.currentIndex;
        }
        
        if (status.records) {
            this.videoRecords = status.records;
            this.updateRecordsUI();
            this.saveRecords();
        }
        
        this.updateUI();
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    new BilibiliAutoPlayer();
});
